/* Enhanced Login Page Styles */

/* CSS Variables for consistent theming */
:root {
  --primary-color: #667eea;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-color: #f093fb;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  --dark-color: #1f2937;
  --light-color: #f8fafc;
  --border-radius: 12px;
  --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Login Wrapper with Glassmorphism */
.enhanced-login-wrapper {
  min-height: 100vh;
  background: var(--primary-gradient);
  position: relative;
  overflow: hidden;
}

.enhanced-login-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.15"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="600" cy="100" r="80" fill="url(%23a)"/><circle cx="100" cy="600" r="90" fill="url(%23a)"/></svg>') no-repeat center center;
  background-size: cover;
  opacity: 0.4;
}

/* Additional floating elements for glassmorphism effect */
.enhanced-login-wrapper::after {
  content: '';
  position: absolute;
  top: 20%;
  right: 10%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  filter: blur(40px);
  animation: float 6s ease-in-out infinite;
}

/* Login Container */
.login-container {
  position: relative;
  z-index: 10;
  max-width: 420px;
  width: 100%;
  margin: 0 auto;
  padding: 20px;
}

/* Logo Section */
.logo-section {
  margin-bottom: 2rem;
}

.logo-container {
  margin-bottom: 1rem;
}

.login-logo {
  max-width: 80px;
  height: auto;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.brand-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-subtitle {
  color: rgba(255, 255, 255, 1);
  font-size: 1rem;
  font-weight: 400;
  margin-bottom: 0;
}

/* Login Card with Glassmorphism */
.login-card {
  background: rgba(255, 255, 255, 0.25);
  border-radius: var(--border-radius);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  padding: 2rem;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

/* Glassmorphism inner glow effect */
.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: var(--border-radius);
  pointer-events: none;
  z-index: -1;
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-title {
  color: var(--dark-color);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.login-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0;
}

/* Enhanced Message System with Glassmorphism */
.message-container {
  margin-bottom: 1.5rem;
}

.enhanced-message {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
  transform: translateY(-20px);
  opacity: 0;
  transition: var(--transition);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.enhanced-message.show {
  transform: translateY(0);
  opacity: 1;
}

.enhanced-message.hide {
  transform: translateY(-20px);
  opacity: 0;
}

/* Message Types with Glassmorphism */
.success-message {
  background: rgba(16, 185, 129, 0.15);
  border-left: 4px solid var(--success-color);
  color: #065f46;
}

.error-message {
  background: rgba(239, 68, 68, 0.15);
  border-left: 4px solid var(--error-color);
  color: #991b1b;
}

.info-message {
  background: rgba(59, 130, 246, 0.15);
  border-left: 4px solid var(--info-color);
  color: #1e40af;
}

.loading-message {
  background: rgba(107, 114, 128, 0.15);
  border-left: 4px solid #6b7280;
  color: #374151;
}

/* Message Components */
.message-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.message-text {
  font-size: 0.875rem;
  line-height: 1.4;
}

.message-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  margin-left: 0.5rem;
  border-radius: 4px;
  transition: var(--transition);
}

.message-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Progress Bar */
.message-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  width: 100%;
}

.loading-progress {
  background: linear-gradient(90deg, transparent, rgba(107, 114, 128, 0.5), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes progress-bar {
  from { width: 100%; }
  to { width: 0%; }
}

/* Loading Spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced Form Styles */
.enhanced-form {
  margin-bottom: 1.5rem;
  color: white;
}

.enhanced-form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: white !important;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Hide icons in form labels */
.form-label i.fas {
  display: none;
}

/* Form text colors */
.login-card,
.login-title,
.login-subtitle {
  color: white !important;
}

/* Input text and placeholders */
.form-control,
.enhanced-input,
.enhanced-input::placeholder {
  color: #000000 !important; /* Black text for better readability */
  border-color: rgba(0, 0, 0, 0.3) !important;
  background-color: rgba(255, 255, 255, 0.9) !important; /* Slightly transparent white background */
}

/* Style for input focus */
.enhanced-input:focus {
  border-color: #000000 !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.25) !important;
  background-color: #ffffff !important;
}

/* Input Container */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.enhanced-input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 2.5rem;
  border: 2px solid #e5e7eb;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: #f9fafb;
}

.enhanced-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-container.focused {
  transform: translateY(-1px);
}

.input-container.valid .enhanced-input {
  border-color: var(--success-color);
  background: #f0fdf4;
}

.input-container.invalid .enhanced-input {
  border-color: var(--error-color);
  background: #fef2f2;
}

/* Input Icons - Hidden by default, shown on interaction */
.input-icon {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  font-size: 1rem;
  transition: var(--transition);
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

/* Show icons when input has focus or content */
.input-container.focused .input-icon,
.input-container.has-content .input-icon {
  opacity: 1;
  transform: scale(1);
}

.input-container.focused .input-icon {
  color: var(--primary-color);
}

.input-container.valid .input-icon {
  color: var(--success-color);
}

.input-container.invalid .input-icon {
  color: var(--error-color);
}

/* Password Toggle */
.password-toggle {
  position: absolute;
  right: 2.5rem;
  cursor: pointer;
  color: #6b7280;
  font-size: 1rem;
  transition: var(--transition);
  padding: 0.25rem;
  border-radius: 4px;
}

.password-toggle:hover {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
}

/* Input Validation - Hidden as per user request */
.input-validation {
  display: none !important;
}

.validation-success,
.validation-error {
  display: none !important;
}

.input-container.valid .validation-success,
.input-container.invalid .validation-error {
  display: none !important;
}

/* Field Feedback */
.field-feedback {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  transition: var(--transition);
}

.field-feedback.error {
  color: var(--error-color);
}

/* Enhanced Submit Button with Perfect Centering */
.enhanced-submit-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--border-radius);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.875rem 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 48px;
}

.enhanced-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.enhanced-submit-btn:active {
  transform: translateY(0);
}

.enhanced-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.enhanced-submit-btn.loading {
  background: #6b7280;
}

.enhanced-submit-btn.success {
  background: var(--success-color);
}

/* Button Content Containers */
.btn-content,
.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.btn-text {
  margin-left: 0.5rem;
  color: #ffffff;
}

/* Login Footer */
.login-footer {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.security-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Create Password Link */
.create-password-link {
  margin-top: 1rem;
}

.create-password-link a {
  color: var(--primary-color);
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

.create-password-link a:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.create-password-link i {
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: 15px;
  }
  
  .login-card {
    padding: 1.5rem;
  }
  
  .brand-title {
    font-size: 1.75rem;
  }
  
  .enhanced-input {
    padding: 0.625rem 2.5rem 0.625rem 2rem;
  }
}

/* Animation Classes */
.animate__shakeX {
  animation: shakeX 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shakeX {
  10%, 90% { transform: translate3d(-10px, 0, 0); }
  20%, 80% { transform: translate3d(20px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-40px, 0, 0); }
  40%, 60% { transform: translate3d(40px, 0, 0); }
}

/* Floating animation for glassmorphism elements */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}
