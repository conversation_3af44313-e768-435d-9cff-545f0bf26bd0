-- Script para crear la tabla tb_password_history
-- Base de datos: gestarse_experian

-- Crear la tabla tb_password_history para almacenar el historial de contraseñas
CREATE TABLE IF NOT EXISTS `tb_password_history` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `usuario_id` INT(11) NOT NULL,
    `password_hash` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `is_active` BOOLEAN NOT NULL DEFAULT 1,
    `created_by_ip` VARCHAR(45) DEFAULT NULL,
    `user_agent` VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_usuario_id` (`usuario_id`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_password_history_usuario` 
        FOREIGN KEY (`usuario_id`) 
        REFERENCES `tb_experian_usuarios` (`id`) 
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Índice compuesto para búsquedas rápidas del password activo por usuario
CREATE INDEX idx_usuario_active ON tb_password_history(usuario_id, is_active);

-- Crear tabla para logs de cambios de contraseña (para auditoría)
CREATE TABLE IF NOT EXISTS `tb_password_change_log` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `usuario_id` INT(11) NOT NULL,
    `action` ENUM('created', 'changed', 'reset', 'failed_attempt') NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `ip_address` VARCHAR(45) DEFAULT NULL,
    `user_agent` VARCHAR(255) DEFAULT NULL,
    `details` TEXT DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_usuario_log` (`usuario_id`),
    KEY `idx_action` (`action`),
    KEY `idx_created_at_log` (`created_at`),
    CONSTRAINT `fk_password_log_usuario` 
        FOREIGN KEY (`usuario_id`) 
        REFERENCES `tb_experian_usuarios` (`id`) 
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Verificar que la tabla tb_experian_usuarios existe y tiene la estructura esperada
DESCRIBE tb_experian_usuarios;

-- Mostrar las nuevas tablas creadas
SHOW TABLES LIKE 'tb_password%';